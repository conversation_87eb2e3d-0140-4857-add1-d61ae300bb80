<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

$error = "";

// สร้าง CSRF token
$csrf_token = generateCSRFToken();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // ล็อกอิน
    error_log("Login attempt: " . $_POST["username"]);
    
    // ตรวจสอบ CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = "Invalid form submission - CSRF token mismatch";
        error_log("CSRF validation failed");
    } else {
        $username = trim($_POST["username"]);
        $password = $_POST["password"];
        
        // ตรวจสอบการล๊อกอิน
        if (isLoginLocked($username)) {
            $error = "Too many failed login attempts. Please try again later.";
        } else {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user) {
                error_log("User found, checking password");
                $verify_result = password_verify($password, $user["password_hash"]);
                error_log("Password verify result: " . ($verify_result ? "TRUE" : "FALSE"));
                
                if ($verify_result) {
                    // ล็อกสำเร็จ
                    $_SESSION["user_id"] = $user["id"];
                    $_SESSION["username"] = $user["username"];
                    $_SESSION["role"] = $user["role"];
                    
                    error_log("Login successful for user: " . $username);
                    
                    // ลบ CSRF token เก่าและสร้างใหม่
                    unset($_SESSION['csrf_token']);
                    
                    header("Location: dashboard.php");
                    exit();
                } else {
                    // ความพยายามล็อก ่ล้มเหลว
                    logFailedLogin($username);
                    error_log("Password verification failed for user: " . $username);
                    $error = "Invalid username or password";
                }
            } else {
                error_log("User not found: " . $username);
                $error = "Invalid username or password";
            }
        }
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo i {
            font-size: 48px;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <i class="fas fa-shipping-container"></i>
            <h3>Container System</h3>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            <div class="form-group mb-3">
                <label>Username</label>
                <input type="text" name="username" class="form-control" required autofocus>
            </div>
            <div class="form-group mb-3">
                <label>Password</label>
                <input type="password" name="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>
    </div>
</body>
</html>
